<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Storage;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'role',
        'avatar',
        'birth_date',
        'gender',
        'address',
        'is_active',
        'last_login_at',
        'otp_code',
        'otp_expires_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'otp_code',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'birth_date' => 'date',
        'last_login_at' => 'datetime',
        'otp_expires_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * Role constants
     */
    const ROLE_ADMIN = 'admin';
    const ROLE_STAFF = 'staff';
    const ROLE_PENJUAL = 'penjual';
    const ROLE_PEMBELI = 'pembeli';

    /**
     * Get all available roles
     */
    public static function getRoles(): array
    {
        return [
            self::ROLE_ADMIN => 'Administrator',
            self::ROLE_STAFF => 'Staff',
            self::ROLE_PENJUAL => 'Penjual Event',
            self::ROLE_PEMBELI => 'Pembeli',
        ];
    }

    /**
     * Check if user has specific role
     */
    public function hasRole(string $role): bool
    {
        return $this->role === $role;
    }

    /**
     * Check if user is admin
     */
    public function isAdmin(): bool
    {
        return $this->hasRole(self::ROLE_ADMIN);
    }

    /**
     * Check if user is staff
     */
    public function isStaff(): bool
    {
        return $this->hasRole(self::ROLE_STAFF);
    }

    /**
     * Check if user is penjual
     */
    public function isPenjual(): bool
    {
        return $this->hasRole(self::ROLE_PENJUAL);
    }

    /**
     * Check if user is pembeli
     */
    public function isPembeli(): bool
    {
        return $this->hasRole(self::ROLE_PEMBELI);
    }

    /**
     * Get user's avatar URL
     */
    public function getAvatarUrlAttribute(): string
    {
        // Check if user has uploaded avatar
        if ($this->avatar) {
            // Try Storage facade first
            if (Storage::exists('public/' . $this->avatar)) {
                return Storage::url($this->avatar);
            }
            // Fallback to file_exists check
            if (file_exists(public_path('storage/' . $this->avatar))) {
                return asset('storage/' . $this->avatar);
            }
        }

        // Generate avatar using UI Avatars service
        $name = urlencode($this->name);
        $background = 'A8D5BA'; // Primary color (green pasta theme)
        $color = 'FFFFFF';

        return "https://ui-avatars.com/api/?name={$name}&background={$background}&color={$color}&size=200&font-size=0.6&rounded=true&bold=true";
    }

    /**
     * Get profile photo URL (alias for avatar)
     */
    public function getProfilePhotoUrlAttribute(): string
    {
        return $this->avatar_url;
    }

    /**
     * Events organized by this user
     */
    public function organizedEvents(): HasMany
    {
        return $this->hasMany(Event::class, 'organizer_id');
    }

    /**
     * Orders made by this user
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Tickets owned by this user
     */
    public function tickets(): HasMany
    {
        return $this->hasMany(Ticket::class, 'buyer_id');
    }

    /**
     * Tickets validated by this user (for staff)
     */
    public function validatedTickets(): HasMany
    {
        return $this->hasMany(Ticket::class, 'validated_by');
    }

    /**
     * Generate OTP for user
     */
    public function generateOtp(): string
    {
        $otp = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);

        $this->update([
            'otp_code' => $otp,
            'otp_expires_at' => now()->addMinutes(10),
        ]);

        return $otp;
    }

    /**
     * Verify OTP
     */
    public function verifyOtp(string $otp): bool
    {
        if ($this->otp_code === $otp && $this->otp_expires_at && $this->otp_expires_at->isFuture()) {
            $this->update([
                'otp_code' => null,
                'otp_expires_at' => null,
                'email_verified_at' => now(),
            ]);

            return true;
        }

        return false;
    }

    /**
     * Update last login timestamp
     */
    public function updateLastLogin(): void
    {
        $this->update(['last_login_at' => now()]);
    }

    /**
     * Scope for active users
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for specific role
     */
    public function scopeRole($query, string $role)
    {
        return $query->where('role', $role);
    }
}
