@extends('layouts.app')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Dashboard Header -->
    <div class="mb-8" data-aos="fade-up">
        <h1 class="text-2xl font-bold mb-2">Dashboard Staff</h1>
        <p class="text-gray-600">Kelola validasi tiket dan pantau aktivitas event</p>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <!-- Scanner -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="100">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-primary/10 rounded-lg">
                    <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"/>
                    </svg>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">QR Scanner</p>
                    <p class="text-lg font-semibold">Validasi Tiket</p>
                </div>
            </div>
            <a href="{{ route('staff.scanner') }}" 
               class="block w-full bg-primary text-white text-center py-2 rounded-lg hover:bg-primary/90 transition-colors">
                Buka Scanner
            </a>
        </div>

        <!-- Today's Stats -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="200">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Tiket Hari Ini</p>
                    <p class="text-2xl font-bold">0</p>
                </div>
            </div>
            <div class="flex items-center text-sm">
                <span class="text-green-600 font-medium">0 Valid</span>
                <span class="text-gray-400 mx-2">•</span>
                <span class="text-red-600">0 Invalid</span>
            </div>
        </div>

        <!-- Active Events -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="300">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Event Aktif</p>
                    <p class="text-2xl font-bold">0</p>
                </div>
            </div>
            <div class="flex items-center text-sm">
                <span class="text-blue-600 font-medium">Hari ini</span>
            </div>
        </div>
    </div>

    <!-- Recent Validations -->
    <div class="bg-white rounded-xl shadow-sm overflow-hidden mb-8" data-aos="fade-up" data-aos-delay="400">
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-lg font-semibold">Validasi Terbaru</h2>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 text-sm bg-primary text-white rounded-lg">Hari Ini</button>
                    <button class="px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-lg">Minggu Ini</button>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Tiket
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Event
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Pembeli
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Waktu
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                                <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                </svg>
                                <p>Belum ada validasi tiket hari ini</p>
                                <p class="text-sm mt-1">Gunakan scanner untuk memvalidasi tiket</p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Today's Events -->
    <div class="bg-white rounded-xl shadow-sm overflow-hidden" data-aos="fade-up" data-aos-delay="500">
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-lg font-semibold">Event Hari Ini</h2>
                <span class="text-sm text-gray-600">{{ now()->format('d M Y') }}</span>
            </div>
            
            <div class="space-y-4">
                <div class="text-center py-12">
                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                    <p class="text-gray-600">Tidak ada event yang berlangsung hari ini</p>
                    <p class="text-sm text-gray-500 mt-1">Event akan muncul di sini saat jadwal dimulai</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Help -->
    <div class="mt-8 bg-gradient-to-r from-primary/10 to-primary/5 rounded-xl p-6" data-aos="fade-up" data-aos-delay="600">
        <div class="flex items-start space-x-4">
            <div class="p-2 bg-primary/20 rounded-lg">
                <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
            </div>
            <div class="flex-1">
                <h3 class="font-semibold text-gray-900 mb-2">Panduan Cepat</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                    <div>
                        <h4 class="font-medium text-gray-900 mb-1">Validasi Tiket:</h4>
                        <ul class="space-y-1">
                            <li>• Buka scanner QR code</li>
                            <li>• Arahkan kamera ke QR tiket</li>
                            <li>• Sistem akan otomatis memvalidasi</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 mb-1">Tips Penting:</h4>
                        <ul class="space-y-1">
                            <li>• Pastikan pencahayaan cukup</li>
                            <li>• QR code dalam kondisi baik</li>
                            <li>• Periksa status tiket sebelum validasi</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scanner Modal (if needed) -->
<div id="scanner-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">QR Scanner</h3>
                <button onclick="closeScanner()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <div id="scanner-container" class="aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
                <p class="text-gray-500">Scanner akan dimuat di sini</p>
            </div>
            <div class="mt-4 text-center">
                <p class="text-sm text-gray-600">Arahkan kamera ke QR code tiket</p>
            </div>
        </div>
    </div>
</div>

<script>
function openScanner() {
    document.getElementById('scanner-modal').classList.remove('hidden');
    // Initialize scanner here
}

function closeScanner() {
    document.getElementById('scanner-modal').classList.add('hidden');
    // Stop scanner here
}
</script>
@endsection
