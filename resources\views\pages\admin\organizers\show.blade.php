@extends('layouts.app')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Page Header -->
    <div class="mb-8" data-aos="fade-up">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold mb-2">Detail Organizer</h1>
                <p class="text-gray-600">Informasi lengkap organizer: {{ $organizer->name }}</p>
            </div>
            <div class="flex space-x-3">
                @if(!$organizer->email_verified_at)
                <form action="{{ route('admin.organizers.approve', $organizer) }}" method="POST" class="inline">
                    @csrf
                    <button type="submit" 
                            class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                            onclick="return confirm('Approve organizer ini?')">
                        Approve Organizer
                    </button>
                </form>
                @endif
                
                @if($organizer->is_active)
                <form action="{{ route('admin.organizers.suspend', $organizer) }}" method="POST" class="inline">
                    @csrf
                    <button type="submit" 
                            class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                            onclick="return confirm('Suspend organizer ini?')">
                        Suspend
                    </button>
                </form>
                @else
                <form action="{{ route('admin.organizers.activate', $organizer) }}" method="POST" class="inline">
                    @csrf
                    <button type="submit" 
                            class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                            onclick="return confirm('Activate organizer ini?')">
                        Activate
                    </button>
                </form>
                @endif
                
                <a href="{{ route('admin.organizers.index') }}" 
                   class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    Kembali
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Organizer Profile -->
        <div class="lg:col-span-1">
            <!-- Basic Info -->
            <div class="bg-white rounded-xl shadow-sm p-6 mb-6" data-aos="fade-up">
                <div class="text-center mb-6">
                    <div class="w-24 h-24 bg-gray-200 rounded-full mx-auto flex items-center justify-center mb-4">
                        <span class="text-3xl font-bold text-gray-600">
                            {{ substr($organizer->name, 0, 1) }}
                        </span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900">{{ $organizer->name }}</h3>
                    <p class="text-gray-600">{{ $organizer->email }}</p>
                </div>
                
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Status:</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {{ $organizer->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $organizer->is_active ? 'Aktif' : 'Tidak Aktif' }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600">Email Verified:</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {{ $organizer->email_verified_at ? 'bg-blue-100 text-blue-800' : 'bg-orange-100 text-orange-800' }}">
                            {{ $organizer->email_verified_at ? 'Verified' : 'Pending' }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600">Phone:</span>
                        <span class="text-gray-900">{{ $organizer->phone ?? 'Tidak ada' }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600">Bergabung:</span>
                        <span class="text-gray-900">{{ $organizer->created_at->format('d M Y') }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600">Last Login:</span>
                        <span class="text-gray-900">{{ $organizer->last_login_at ? $organizer->last_login_at->format('d M Y, H:i') : 'Belum pernah' }}</span>
                    </div>
                </div>
            </div>

            <!-- Analytics -->
            <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="100">
                <h4 class="text-lg font-semibold mb-4">Analytics</h4>
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Total Events:</span>
                        <span class="text-2xl font-bold text-blue-600">{{ $analytics['total_events'] }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Total Revenue:</span>
                        <span class="text-2xl font-bold text-green-600">Rp {{ number_format($analytics['total_revenue'], 0, ',', '.') }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Tickets Sold:</span>
                        <span class="text-2xl font-bold text-purple-600">{{ number_format($analytics['total_tickets_sold'], 0, ',', '.') }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Avg Rating:</span>
                        <div class="flex items-center">
                            <span class="text-2xl font-bold text-yellow-600">{{ $analytics['avg_event_rating'] }}</span>
                            <div class="flex ml-2">
                                @for($i = 1; $i <= 5; $i++)
                                <svg class="w-4 h-4 {{ $i <= $analytics['avg_event_rating'] ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                                @endfor
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Events & Activity -->
        <div class="lg:col-span-2">
            <!-- Events List -->
            <div class="bg-white rounded-xl shadow-sm p-6 mb-6" data-aos="fade-up" data-aos-delay="200">
                <div class="flex justify-between items-center mb-6">
                    <h4 class="text-lg font-semibold">Events</h4>
                    <span class="text-sm text-gray-600">{{ $organizer->organizedEvents->count() }} total events</span>
                </div>
                
                @if($organizer->organizedEvents->count() > 0)
                <div class="space-y-4">
                    @foreach($organizer->organizedEvents->take(5) as $event)
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h5 class="font-medium text-gray-900">{{ $event->title }}</h5>
                                <p class="text-sm text-gray-600 mt-1">{{ $event->category->name ?? 'No Category' }}</p>
                                <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                                    <span>{{ $event->start_date ? $event->start_date->format('d M Y') : 'No Date' }}</span>
                                    <span>{{ $event->tickets->count() }} tickets sold</span>
                                    <span>Rp {{ number_format($event->price, 0, ',', '.') }}</span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {{ $event->status === 'published' ? 'bg-green-100 text-green-800' : 
                                       ($event->status === 'draft' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800') }}">
                                    {{ ucfirst($event->status) }}
                                </span>
                            </div>
                        </div>
                    </div>
                    @endforeach
                    
                    @if($organizer->organizedEvents->count() > 5)
                    <div class="text-center">
                        <button class="text-primary hover:text-primary/80 text-sm font-medium">
                            Lihat semua events ({{ $organizer->organizedEvents->count() - 5 }} lainnya)
                        </button>
                    </div>
                    @endif
                </div>
                @else
                <div class="text-center py-8">
                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                    <p class="text-gray-500">Belum ada event yang dibuat</p>
                </div>
                @endif
            </div>

            <!-- Recent Activity -->
            <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="300">
                <h4 class="text-lg font-semibold mb-6">Recent Activity</h4>
                
                <div class="space-y-4">
                    <!-- Account Created -->
                    <div class="flex items-start space-x-3">
                        <div class="p-2 bg-blue-100 rounded-lg">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm text-gray-900">Account created</p>
                            <p class="text-xs text-gray-500">{{ $organizer->created_at->format('d M Y, H:i') }}</p>
                        </div>
                    </div>

                    @if($organizer->email_verified_at)
                    <!-- Email Verified -->
                    <div class="flex items-start space-x-3">
                        <div class="p-2 bg-green-100 rounded-lg">
                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm text-gray-900">Email verified</p>
                            <p class="text-xs text-gray-500">{{ $organizer->email_verified_at->format('d M Y, H:i') }}</p>
                        </div>
                    </div>
                    @endif

                    @foreach($organizer->organizedEvents->take(3) as $event)
                    <!-- Event Created -->
                    <div class="flex items-start space-x-3">
                        <div class="p-2 bg-purple-100 rounded-lg">
                            <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm text-gray-900">Created event: {{ $event->title }}</p>
                            <p class="text-xs text-gray-500">{{ $event->created_at->format('d M Y, H:i') }}</p>
                        </div>
                    </div>
                    @endforeach

                    @if($organizer->last_login_at)
                    <!-- Last Login -->
                    <div class="flex items-start space-x-3">
                        <div class="p-2 bg-gray-100 rounded-lg">
                            <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"/>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm text-gray-900">Last login</p>
                            <p class="text-xs text-gray-500">{{ $organizer->last_login_at->format('d M Y, H:i') }}</p>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
