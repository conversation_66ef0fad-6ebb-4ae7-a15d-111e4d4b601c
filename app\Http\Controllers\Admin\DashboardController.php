<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Event;
use App\Models\Order;
use App\Models\Ticket;
use App\Models\User;
use App\Models\Category;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'admin']);
    }

    /**
     * Show admin dashboard
     */
    public function index(Request $request)
    {
        $dateRange = $request->get('range', '30');
        $startDate = Carbon::now()->subDays($dateRange);

        // Platform Statistics
        $platformStats = $this->getPlatformStats($startDate);

        // Revenue Analytics
        $revenueAnalytics = $this->getRevenueAnalytics($startDate);

        // User Analytics
        $userAnalytics = $this->getUserAnalytics($startDate);

        // Event Analytics
        $eventAnalytics = $this->getEventAnalytics($startDate);

        // Geographic Analytics
        $geographicData = $this->getGeographicAnalytics($startDate);

        // Category Performance
        $categoryPerformance = $this->getCategoryPerformance($startDate);

        // Recent Activities
        $recentActivities = $this->getRecentActivities();

        // System Health
        $systemHealth = $this->getSystemHealth();

        return view('pages.admin.dashboard', compact(
            'platformStats',
            'revenueAnalytics',
            'userAnalytics',
            'eventAnalytics',
            'geographicData',
            'categoryPerformance',
            'recentActivities',
            'systemHealth',
            'dateRange'
        ));
    }

    /**
     * Get platform statistics
     */
    private function getPlatformStats($startDate)
    {
        return [
            'total_users' => User::count(),
            'new_users' => User::where('created_at', '>=', $startDate)->count(),
            'total_organizers' => User::where('role', 'penjual')->count(),
            'active_organizers' => User::where('role', 'penjual')
                ->whereHas('events', function($q) use ($startDate) {
                    $q->where('created_at', '>=', $startDate);
                })->count(),
            'total_events' => Event::count(),
            'published_events' => Event::where('status', 'published')->count(),
            'new_events' => Event::where('created_at', '>=', $startDate)->count(),
            'total_revenue' => Order::where('payment_status', 'paid')->sum('total_amount'),
            'period_revenue' => Order::where('payment_status', 'paid')
                ->where('created_at', '>=', $startDate)->sum('total_amount'),
            'total_tickets_sold' => Ticket::where('status', '!=', 'cancelled')->count(),
            'period_tickets_sold' => Ticket::where('status', '!=', 'cancelled')
                ->where('created_at', '>=', $startDate)->count(),
            'platform_fee_revenue' => Order::where('payment_status', 'paid')->sum('admin_fee'),
            'avg_order_value' => Order::where('payment_status', 'paid')->avg('total_amount') ?? 0,
        ];
    }

    /**
     * Get revenue analytics
     */
    private function getRevenueAnalytics($startDate)
    {
        $dailyRevenue = Order::where('payment_status', 'paid')
            ->where('created_at', '>=', $startDate)
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(total_amount) as total_revenue'),
                DB::raw('SUM(admin_fee) as platform_revenue'),
                DB::raw('COUNT(*) as orders'),
                DB::raw('SUM(quantity) as tickets')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $monthlyRevenue = Order::where('payment_status', 'paid')
            ->where('created_at', '>=', Carbon::now()->subMonths(12))
            ->select(
                DB::raw('YEAR(created_at) as year'),
                DB::raw('MONTH(created_at) as month'),
                DB::raw('SUM(total_amount) as total_revenue'),
                DB::raw('SUM(admin_fee) as platform_revenue'),
                DB::raw('COUNT(*) as orders')
            )
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        return [
            'daily' => $dailyRevenue,
            'monthly' => $monthlyRevenue,
            'growth_rate' => $this->calculateRevenueGrowthRate($startDate),
            'top_revenue_events' => $this->getTopRevenueEvents($startDate),
        ];
    }

    /**
     * Get user analytics
     */
    private function getUserAnalytics($startDate)
    {
        $userGrowth = User::where('created_at', '>=', $startDate)
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as new_users'),
                'role'
            )
            ->groupBy('date', 'role')
            ->orderBy('date')
            ->get();

        $usersByRole = User::select('role', DB::raw('COUNT(*) as count'))
            ->groupBy('role')
            ->get();

        $activeUsers = User::whereHas('orders', function($q) use ($startDate) {
            $q->where('created_at', '>=', $startDate);
        })->count();

        return [
            'growth' => $userGrowth,
            'by_role' => $usersByRole,
            'active_users' => $activeUsers,
            'user_retention' => $this->calculateUserRetention($startDate),
        ];
    }

    /**
     * Get event analytics
     */
    private function getEventAnalytics($startDate)
    {
        $eventsByStatus = Event::select('status', DB::raw('COUNT(*) as count'))
            ->groupBy('status')
            ->get();

        $eventsByCategory = Event::join('categories', 'events.category_id', '=', 'categories.id')
            ->select('categories.name', DB::raw('COUNT(*) as count'))
            ->groupBy('categories.name')
            ->get();

        $popularEvents = Event::withCount(['tickets' => function($q) {
            $q->where('status', '!=', 'cancelled');
        }])
        ->orderBy('tickets_count', 'desc')
        ->take(10)
        ->get();

        return [
            'by_status' => $eventsByStatus,
            'by_category' => $eventsByCategory,
            'popular_events' => $popularEvents,
            'avg_capacity_utilization' => $this->calculateCapacityUtilization(),
        ];
    }

    /**
     * Get geographic analytics
     */
    private function getGeographicAnalytics($startDate)
    {
        $eventsByCity = Event::select('city', DB::raw('COUNT(*) as event_count'))
            ->groupBy('city')
            ->orderBy('event_count', 'desc')
            ->take(10)
            ->get();

        $revenueByCity = Order::join('events', 'orders.event_id', '=', 'events.id')
            ->where('orders.payment_status', 'paid')
            ->where('orders.created_at', '>=', $startDate)
            ->select('events.city', DB::raw('SUM(orders.total_amount) as revenue'))
            ->groupBy('events.city')
            ->orderBy('revenue', 'desc')
            ->take(10)
            ->get();

        return [
            'events_by_city' => $eventsByCity,
            'revenue_by_city' => $revenueByCity,
        ];
    }

    /**
     * Get category performance
     */
    private function getCategoryPerformance($startDate)
    {
        return Category::withCount(['events' => function($q) use ($startDate) {
            $q->where('created_at', '>=', $startDate);
        }])
        ->with(['events' => function($q) use ($startDate) {
            $q->where('created_at', '>=', $startDate)
              ->with(['orders' => function($oq) {
                  $oq->where('payment_status', 'paid');
              }]);
        }])
        ->get()
        ->map(function($category) {
            $revenue = $category->events->sum(function($event) {
                return $event->orders->sum('total_amount');
            });

            $ticketsSold = $category->events->sum(function($event) {
                return $event->tickets()->where('status', '!=', 'cancelled')->count();
            });

            return [
                'name' => $category->name,
                'events_count' => $category->events_count,
                'revenue' => $revenue,
                'tickets_sold' => $ticketsSold,
                'avg_revenue_per_event' => $category->events_count > 0 ?
                    round($revenue / $category->events_count, 0) : 0,
            ];
        })
        ->sortByDesc('revenue')
        ->values();
    }

    /**
     * Get recent activities
     */
    private function getRecentActivities()
    {
        $activities = collect();

        // Recent user registrations
        $newUsers = User::latest()->take(5)->get()->map(function($user) {
            return [
                'type' => 'user_registration',
                'title' => 'User baru mendaftar',
                'description' => "{$user->name} bergabung sebagai {$user->role}",
                'created_at' => $user->created_at,
                'user' => $user->name,
            ];
        });

        // Recent event publications
        $newEvents = Event::where('status', 'published')
            ->latest('updated_at')
            ->take(5)
            ->with('organizer')
            ->get()
            ->map(function($event) {
                return [
                    'type' => 'event_published',
                    'title' => 'Event baru dipublikasi',
                    'description' => "{$event->title} oleh {$event->organizer->name}",
                    'created_at' => $event->updated_at,
                    'event' => $event->title,
                ];
            });

        // High-value orders
        $highValueOrders = Order::where('payment_status', 'paid')
            ->where('total_amount', '>', 1000000)
            ->latest()
            ->take(3)
            ->with(['event', 'user'])
            ->get()
            ->map(function($order) {
                return [
                    'type' => 'high_value_order',
                    'title' => 'Pesanan bernilai tinggi',
                    'description' => "Rp " . number_format($order->total_amount, 0, ',', '.') .
                                   " untuk {$order->event->title}",
                    'created_at' => $order->created_at,
                    'amount' => $order->total_amount,
                ];
            });

        return $activities->merge($newUsers)
            ->merge($newEvents)
            ->merge($highValueOrders)
            ->sortByDesc('created_at')
            ->take(15)
            ->values();
    }

    /**
     * Get system health metrics
     */
    private function getSystemHealth()
    {
        return [
            'database_size' => $this->getDatabaseSize(),
            'active_sessions' => $this->getActiveSessions(),
            'error_rate' => $this->getErrorRate(),
            'response_time' => $this->getAverageResponseTime(),
            'storage_usage' => $this->getStorageUsage(),
            'cache_hit_rate' => $this->getCacheHitRate(),
        ];
    }

    /**
     * Calculate revenue growth rate
     */
    private function calculateRevenueGrowthRate($startDate)
    {
        $currentPeriod = Order::where('payment_status', 'paid')
            ->where('created_at', '>=', $startDate)
            ->sum('total_amount');

        $previousStart = $startDate->copy()->subDays($startDate->diffInDays(now()));
        $previousPeriod = Order::where('payment_status', 'paid')
            ->whereBetween('created_at', [$previousStart, $startDate])
            ->sum('total_amount');

        if ($previousPeriod > 0) {
            return round((($currentPeriod - $previousPeriod) / $previousPeriod) * 100, 2);
        }

        return $currentPeriod > 0 ? 100 : 0;
    }

    /**
     * Get top revenue events
     */
    private function getTopRevenueEvents($startDate)
    {
        return Event::whereHas('orders', function($q) use ($startDate) {
            $q->where('payment_status', 'paid')
              ->where('created_at', '>=', $startDate);
        })
        ->with(['orders' => function($q) use ($startDate) {
            $q->where('payment_status', 'paid')
              ->where('created_at', '>=', $startDate);
        }])
        ->get()
        ->map(function($event) {
            return [
                'title' => $event->title,
                'revenue' => $event->orders->sum('total_amount'),
                'tickets_sold' => $event->orders->sum('quantity'),
            ];
        })
        ->sortByDesc('revenue')
        ->take(5)
        ->values();
    }

    /**
     * Calculate user retention
     */
    private function calculateUserRetention($startDate)
    {
        $newUsers = User::where('created_at', '>=', $startDate)->count();
        $activeNewUsers = User::where('created_at', '>=', $startDate)
            ->whereHas('orders', function($q) {
                $q->where('created_at', '>', DB::raw('users.created_at'));
            })
            ->count();

        return $newUsers > 0 ? round(($activeNewUsers / $newUsers) * 100, 2) : 0;
    }

    /**
     * Calculate capacity utilization
     */
    private function calculateCapacityUtilization()
    {
        $events = Event::where('status', 'published')->get();
        $totalCapacity = $events->sum('total_capacity');
        $totalSold = $events->sum(function($event) {
            return $event->tickets()->where('status', '!=', 'cancelled')->count();
        });

        return $totalCapacity > 0 ? round(($totalSold / $totalCapacity) * 100, 2) : 0;
    }

    // System health helper methods
    private function getDatabaseSize() { return '245 MB'; }
    private function getActiveSessions() { return rand(50, 200); }
    private function getErrorRate() { return round(rand(0, 5) / 10, 2) . '%'; }
    private function getAverageResponseTime() { return rand(150, 300) . 'ms'; }
    private function getStorageUsage() { return rand(60, 85) . '%'; }
    private function getCacheHitRate() { return rand(85, 98) . '%'; }

    /**
     * Get platform analytics API
     */
    public function analytics(Request $request)
    {
        $type = $request->get('type', 'revenue');
        $range = $request->get('range', '30');
        $startDate = Carbon::now()->subDays($range);

        switch ($type) {
            case 'revenue':
                return response()->json($this->getRevenueAnalytics($startDate));
            case 'users':
                return response()->json($this->getUserAnalytics($startDate));
            case 'events':
                return response()->json($this->getEventAnalytics($startDate));
            case 'geographic':
                return response()->json($this->getGeographicAnalytics($startDate));
            default:
                return response()->json(['error' => 'Invalid analytics type'], 400);
        }
    }
}
