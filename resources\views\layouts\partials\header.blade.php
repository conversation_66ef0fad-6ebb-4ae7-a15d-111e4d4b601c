<header class="bg-white/95 backdrop-blur-md shadow-lg fixed w-full top-0 z-40 transition-all duration-300" 
        x-data="{ 
            scrolled: false, 
            mobileMenuOpen: false,
            searchOpen: false,
            searchQuery: '',
            notifications: [],
            showNotifications: false
        }" 
        @scroll.window="scrolled = window.pageYOffset > 50"
        :class="{ 'bg-white/98 shadow-xl': scrolled }">
    
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Logo -->
            <div class="flex items-center space-x-3">
                <a href="{{ route('home') }}" class="flex items-center space-x-3 group">
                    <div class="w-10 h-10 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                        </svg>
                    </div>
                    <h1 class="text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                        TikPro
                    </h1>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden md:flex items-center space-x-8">
                <a href="{{ route('home') }}" 
                   class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium {{ request()->routeIs('home') ? 'text-primary' : '' }}">
                    Beranda
                </a>
                <a href="#events" 
                   class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">
                    Event
                </a>
                <a href="#categories" 
                   class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">
                    Kategori
                </a>
                @auth
                    @if(auth()->user()->isPenjual() || auth()->user()->isAdmin())
                        <a href="{{ route('organizer.dashboard') }}" 
                           class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">
                            Dashboard
                        </a>
                    @endif
                @endauth
            </div>

            <!-- Search Bar (Desktop) -->
            <div class="hidden lg:flex items-center flex-1 max-w-md mx-8">
                <div class="relative w-full">
                    <input type="text" 
                           x-model="searchQuery"
                           placeholder="Cari event..." 
                           class="w-full px-4 py-2 pl-10 pr-4 rounded-xl border-2 border-gray-200 focus:border-primary focus:outline-none transition-all duration-300">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Right Side Actions -->
            <div class="flex items-center space-x-4">
                <!-- Search Button (Mobile) -->
                <button @click="searchOpen = !searchOpen" 
                        class="lg:hidden p-2 text-gray-700 hover:text-primary transition-colors duration-200">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                    </svg>
                </button>

                @auth
                    <!-- Notifications -->
                    <div class="relative" x-data="{ showNotifications: false }">
                        <button @click="showNotifications = !showNotifications" 
                                class="p-2 text-gray-700 hover:text-primary transition-colors duration-200 relative">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
                            </svg>
                            <!-- Notification Badge -->
                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                                3
                            </span>
                        </button>

                        <!-- Notifications Dropdown -->
                        <div x-show="showNotifications" 
                             @click.away="showNotifications = false"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 transform scale-95"
                             x-transition:enter-end="opacity-100 transform scale-100"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 transform scale-100"
                             x-transition:leave-end="opacity-0 transform scale-95"
                             class="absolute right-0 mt-2 w-80 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50">
                            <div class="px-4 py-2 border-b border-gray-200">
                                <h3 class="text-lg font-semibold text-gray-900">Notifikasi</h3>
                            </div>
                            <div class="max-h-64 overflow-y-auto">
                                <!-- Sample Notifications -->
                                <div class="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-900">Tiket Anda untuk Jakarta Music Festival telah dikonfirmasi</p>
                                            <p class="text-xs text-gray-500 mt-1">2 jam yang lalu</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-900">Pembayaran berhasil untuk Digital Marketing Workshop</p>
                                            <p class="text-xs text-gray-500 mt-1">1 hari yang lalu</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="px-4 py-3 hover:bg-gray-50 cursor-pointer">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-900">Event baru: Bali Food Festival tersedia</p>
                                            <p class="text-xs text-gray-500 mt-1">3 hari yang lalu</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="px-4 py-2 border-t border-gray-200">
                                <a href="#" class="text-sm text-primary hover:text-accent font-medium">Lihat semua notifikasi</a>
                            </div>
                        </div>
                    </div>

                    <!-- User Menu -->
                    <div class="relative" x-data="{ showUserMenu: false }">
                        <button @click="showUserMenu = !showUserMenu" 
                                class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                            <img src="{{ auth()->user()->avatar_url }}" 
                                 alt="{{ auth()->user()->name }}" 
                                 class="w-8 h-8 rounded-full object-cover">
                            <span class="hidden md:block text-sm font-medium text-gray-700">{{ auth()->user()->name }}</span>
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>

                        <!-- User Dropdown -->
                        <div x-show="showUserMenu" 
                             @click.away="showUserMenu = false"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 transform scale-95"
                             x-transition:enter-end="opacity-100 transform scale-100"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 transform scale-100"
                             x-transition:leave-end="opacity-0 transform scale-95"
                             class="absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50">
                            <div class="px-4 py-2 border-b border-gray-200">
                                <p class="text-sm font-medium text-gray-900">{{ auth()->user()->name }}</p>
                                <p class="text-xs text-gray-500">{{ auth()->user()->email }}</p>
                                <span class="inline-block px-2 py-1 text-xs bg-primary/10 text-primary rounded-full mt-1">
                                    {{ ucfirst(auth()->user()->role) }}
                                </span>
                            </div>
                            <a href="{{ route('profile') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Profil Saya</a>
                            <a href="{{ route('my-tickets') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Tiket Saya</a>
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Riwayat Pembelian</a>
                            <a href="{{ route('settings') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Pengaturan</a>
                            <div class="border-t border-gray-200 mt-2 pt-2">
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                        Logout
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                @else
                    <!-- Guest Actions -->
                    <div class="hidden md:flex items-center space-x-4">
                        <a href="{{ route('login') }}" 
                           class="text-gray-700 hover:text-primary px-4 py-2 rounded-lg transition-colors duration-200 font-medium">
                            Masuk
                        </a>
                        <a href="{{ route('register') }}" 
                           class="bg-gradient-to-r from-primary to-secondary text-white hover:shadow-lg px-6 py-2 rounded-lg transition-all duration-200 font-medium transform hover:scale-105">
                            Daftar
                        </a>
                    </div>
                @endauth

                <!-- Mobile menu button -->
                <button @click="mobileMenuOpen = !mobileMenuOpen" 
                        class="md:hidden p-2 text-gray-700 hover:text-primary transition-colors duration-200">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path x-show="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                        <path x-show="mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile Search Bar -->
    <div x-show="searchOpen" 
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 transform -translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-150"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform -translate-y-2"
         class="lg:hidden border-t border-gray-200 px-4 py-3">
        <div class="relative">
            <input type="text" 
                   x-model="searchQuery"
                   placeholder="Cari event..." 
                   class="w-full px-4 py-2 pl-10 pr-4 rounded-xl border-2 border-gray-200 focus:border-primary focus:outline-none transition-all duration-300">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                </svg>
            </div>
        </div>
    </div>

    <!-- Mobile Navigation Menu -->
    <div x-show="mobileMenuOpen" 
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 transform -translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-150"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform -translate-y-2"
         class="md:hidden bg-white border-t border-gray-200">
        <div class="px-4 py-6 space-y-4">
            <a href="{{ route('home') }}" class="block text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Beranda</a>
            <a href="#events" class="block text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Event</a>
            <a href="#categories" class="block text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Kategori</a>
            
            @auth
                @if(auth()->user()->isPenjual() || auth()->user()->isAdmin())
                    <a href="{{ route('organizer.dashboard') }}" class="block text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Dashboard</a>
                @endif
            @else
                <div class="pt-4 border-t border-gray-200 space-y-2">
                    <a href="{{ route('login') }}" 
                       class="block w-full text-center bg-gray-100 text-gray-700 px-4 py-2 rounded-lg transition-colors duration-200">
                        Masuk
                    </a>
                    <a href="{{ route('register') }}" 
                       class="block w-full text-center bg-gradient-to-r from-primary to-secondary text-white px-4 py-2 rounded-lg transition-all duration-200">
                        Daftar
                    </a>
                </div>
            @endauth
        </div>
    </div>
</header>

<!-- Header Spacer -->
<div class="h-16"></div>
