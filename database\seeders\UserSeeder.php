<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating user accounts for each role...');

        // Create Admin Users
        $this->createAdminUsers();

        // Create Staff Users
        $this->createStaffUsers();

        // Create Penjual (Event Organizer) Users
        $this->createPenjualUsers();

        // Create Pembeli (Customer) Users
        $this->createPembeliUsers();

        $this->command->info('User accounts created successfully!');
        $this->displayCredentials();
    }

    /**
     * Create Admin Users
     */
    private function createAdminUsers(): void
    {
        $admins = [
            [
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'phone' => '************',
                'gender' => 'male',
                'birth_date' => '1985-01-15',
                'address' => 'Jl. Sudirman No. 1, Jakarta Pusat, DKI Jakarta',
            ],
            [
                'name' => 'Admin Sarah',
                'email' => '<EMAIL>',
                'phone' => '************',
                'gender' => 'female',
                'birth_date' => '1988-03-22',
                'address' => 'Jl. Thamrin No. 5, Jakarta Pusat, DKI Jakarta',
            ],
        ];

        foreach ($admins as $admin) {
            User::create(array_merge($admin, [
                'password' => Hash::make('admin123'),
                'role' => User::ROLE_ADMIN,
                'email_verified_at' => now(),
                'is_active' => true,
                'last_login_at' => now()->subDays(rand(1, 7)),
            ]));
        }

        $this->command->info('✓ Admin users created');
    }

    /**
     * Create Staff Users
     */
    private function createStaffUsers(): void
    {
        $staffs = [
            [
                'name' => 'Staff Budi',
                'email' => '<EMAIL>',
                'phone' => '081234567892',
                'gender' => 'male',
                'birth_date' => '1992-06-10',
                'address' => 'Jl. Gatot Subroto No. 15, Jakarta Selatan, DKI Jakarta',
            ],
            [
                'name' => 'Staff Sari',
                'email' => '<EMAIL>',
                'phone' => '081234567893',
                'gender' => 'female',
                'birth_date' => '1990-09-18',
                'address' => 'Jl. Kuningan No. 8, Jakarta Selatan, DKI Jakarta',
            ],
            [
                'name' => 'Staff Andi',
                'email' => '<EMAIL>',
                'phone' => '081234567894',
                'gender' => 'male',
                'birth_date' => '1993-12-05',
                'address' => 'Jl. Kemang No. 12, Jakarta Selatan, DKI Jakarta',
            ],
        ];

        foreach ($staffs as $staff) {
            User::create(array_merge($staff, [
                'password' => Hash::make('staff123'),
                'role' => User::ROLE_STAFF,
                'email_verified_at' => now(),
                'is_active' => true,
                'last_login_at' => now()->subDays(rand(1, 5)),
            ]));
        }

        $this->command->info('✓ Staff users created');
    }

    /**
     * Create Penjual (Event Organizer) Users
     */
    private function createPenjualUsers(): void
    {
        $penjuals = [
            [
                'name' => 'Event Organizer Pro',
                'email' => '<EMAIL>',
                'phone' => '081234567895',
                'gender' => 'male',
                'birth_date' => '1987-04-12',
                'address' => 'Jl. Senopati No. 25, Jakarta Selatan, DKI Jakarta',
            ],
            [
                'name' => 'Maya Event Planner',
                'email' => '<EMAIL>',
                'phone' => '081234567896',
                'gender' => 'female',
                'birth_date' => '1989-08-30',
                'address' => 'Jl. SCBD No. 10, Jakarta Selatan, DKI Jakarta',
            ],
            [
                'name' => 'Rizki Entertainment',
                'email' => '<EMAIL>',
                'phone' => '081234567897',
                'gender' => 'male',
                'birth_date' => '1986-11-20',
                'address' => 'Jl. Pondok Indah No. 7, Jakarta Selatan, DKI Jakarta',
            ],
            [
                'name' => 'Sinta Creative Events',
                'email' => '<EMAIL>',
                'phone' => '081234567898',
                'gender' => 'female',
                'birth_date' => '1991-02-14',
                'address' => 'Jl. Menteng No. 18, Jakarta Pusat, DKI Jakarta',
            ],
            [
                'name' => 'Dedi Music Production',
                'email' => '<EMAIL>',
                'phone' => '081234567899',
                'gender' => 'male',
                'birth_date' => '1984-07-08',
                'address' => 'Jl. Kelapa Gading No. 22, Jakarta Utara, DKI Jakarta',
            ],
        ];

        foreach ($penjuals as $penjual) {
            User::create(array_merge($penjual, [
                'password' => Hash::make('penjual123'),
                'role' => User::ROLE_PENJUAL,
                'email_verified_at' => now(),
                'is_active' => true,
                'last_login_at' => now()->subDays(rand(1, 3)),
            ]));
        }

        $this->command->info('✓ Penjual (Event Organizer) users created');
    }

    /**
     * Create Pembeli (Customer) Users
     */
    private function createPembeliUsers(): void
    {
        $pembelis = [
            [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'phone' => '081234567900',
                'gender' => 'male',
                'birth_date' => '1995-05-15',
                'address' => 'Jl. Cikini No. 45, Jakarta Pusat, DKI Jakarta',
            ],
            [
                'name' => 'Jane Smith',
                'email' => '<EMAIL>',
                'phone' => '081234567901',
                'gender' => 'female',
                'birth_date' => '1993-09-22',
                'address' => 'Jl. Tebet No. 33, Jakarta Selatan, DKI Jakarta',
            ],
            [
                'name' => 'Ahmad Fauzi',
                'email' => '<EMAIL>',
                'phone' => '081234567902',
                'gender' => 'male',
                'birth_date' => '1992-12-03',
                'address' => 'Jl. Cempaka Putih No. 12, Jakarta Pusat, DKI Jakarta',
            ],
            [
                'name' => 'Siti Nurhaliza',
                'email' => '<EMAIL>',
                'phone' => '081234567903',
                'gender' => 'female',
                'birth_date' => '1994-03-18',
                'address' => 'Jl. Pasar Minggu No. 28, Jakarta Selatan, DKI Jakarta',
            ],
            [
                'name' => 'Budi Santoso',
                'email' => '<EMAIL>',
                'phone' => '081234567904',
                'gender' => 'male',
                'birth_date' => '1996-01-25',
                'address' => 'Jl. Rawamangun No. 15, Jakarta Timur, DKI Jakarta',
            ],
            [
                'name' => 'Dewi Lestari',
                'email' => '<EMAIL>',
                'phone' => '081234567905',
                'gender' => 'female',
                'birth_date' => '1997-06-12',
                'address' => 'Jl. Cipinang No. 8, Jakarta Timur, DKI Jakarta',
            ],
            [
                'name' => 'Rudi Hermawan',
                'email' => '<EMAIL>',
                'phone' => '081234567906',
                'gender' => 'male',
                'birth_date' => '1998-10-30',
                'address' => 'Jl. Sunter No. 20, Jakarta Utara, DKI Jakarta',
            ],
            [
                'name' => 'Lisa Permata',
                'email' => '<EMAIL>',
                'phone' => '081234567907',
                'gender' => 'female',
                'birth_date' => '1999-04-07',
                'address' => 'Jl. Pluit No. 35, Jakarta Utara, DKI Jakarta',
            ],
        ];

        foreach ($pembelis as $pembeli) {
            User::create(array_merge($pembeli, [
                'password' => Hash::make('pembeli123'),
                'role' => User::ROLE_PEMBELI,
                'email_verified_at' => now(),
                'is_active' => true,
                'last_login_at' => now()->subDays(rand(1, 10)),
            ]));
        }

        $this->command->info('✓ Pembeli (Customer) users created');
    }

    /**
     * Display login credentials for all created users
     */
    private function displayCredentials(): void
    {
        $this->command->info('');
        $this->command->info('=== LOGIN CREDENTIALS ===');
        $this->command->info('');

        $this->command->info('🔑 ADMIN ACCOUNTS:');
        $this->command->info('Email: <EMAIL> | Password: admin123');
        $this->command->info('Email: <EMAIL> | Password: admin123');
        $this->command->info('');

        $this->command->info('👥 STAFF ACCOUNTS:');
        $this->command->info('Email: <EMAIL> | Password: staff123');
        $this->command->info('Email: <EMAIL> | Password: staff123');
        $this->command->info('Email: <EMAIL> | Password: staff123');
        $this->command->info('');

        $this->command->info('🎪 PENJUAL (EVENT ORGANIZER) ACCOUNTS:');
        $this->command->info('Email: <EMAIL> | Password: penjual123');
        $this->command->info('Email: <EMAIL> | Password: penjual123');
        $this->command->info('Email: <EMAIL> | Password: penjual123');
        $this->command->info('Email: <EMAIL> | Password: penjual123');
        $this->command->info('Email: <EMAIL> | Password: penjual123');
        $this->command->info('');

        $this->command->info('🛒 PEMBELI (CUSTOMER) ACCOUNTS:');
        $this->command->info('Email: <EMAIL> | Password: pembeli123');
        $this->command->info('Email: <EMAIL> | Password: pembeli123');
        $this->command->info('Email: <EMAIL> | Password: pembeli123');
        $this->command->info('Email: <EMAIL> | Password: pembeli123');
        $this->command->info('Email: <EMAIL> | Password: pembeli123');
        $this->command->info('Email: <EMAIL> | Password: pembeli123');
        $this->command->info('Email: <EMAIL> | Password: pembeli123');
        $this->command->info('Email: <EMAIL> | Password: pembeli123');
        $this->command->info('');

        $this->command->info('📊 SUMMARY:');
        $this->command->info('Total Admin: 2 users');
        $this->command->info('Total Staff: 3 users');
        $this->command->info('Total Penjual: 5 users');
        $this->command->info('Total Pembeli: 8 users');
        $this->command->info('Total Users: 18 users');
        $this->command->info('');
        $this->command->info('🌐 Access the application at: http://localhost/Project-tikpro.my.id');
        $this->command->info('');
    }
}
