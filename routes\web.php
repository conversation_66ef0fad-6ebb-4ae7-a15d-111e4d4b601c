<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Auth\PasswordResetController;
use App\Http\Controllers\EventController;
use App\Http\Controllers\Organizer\EventController as OrganizerEventController;
use App\Http\Controllers\TicketController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Organizer\DashboardController as OrganizerDashboardController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Public Routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/search', [HomeController::class, 'search'])->name('search');
Route::get('/category/{category}', [HomeController::class, 'eventsByCategory'])->name('category');
Route::get('/nearby', [HomeController::class, 'nearbyEvents'])->name('nearby');

// PWA Routes
Route::get('/offline', function () {
    return view('offline');
})->name('offline');

// AJAX Routes
Route::get('/api/featured-events', [HomeController::class, 'featuredEvents'])->name('api.featured-events');

// Event Routes (Public)
Route::prefix('events')->name('events.')->group(function () {
    Route::get('/', [EventController::class, 'index'])->name('index');
    Route::get('/search', [EventController::class, 'search'])->name('search');
    Route::get('/featured', [EventController::class, 'featured'])->name('featured');
    Route::get('/upcoming', [EventController::class, 'upcoming'])->name('upcoming');
    Route::get('/nearby', [EventController::class, 'nearby'])->name('nearby');
    Route::get('/category/{category}', [EventController::class, 'byCategory'])->name('category');
    Route::get('/{event}', [EventController::class, 'show'])->name('show');

    // Wishlist routes (requires auth)
    Route::middleware('auth')->group(function () {
        Route::post('/{event}/wishlist', [EventController::class, 'addToWishlist'])->name('wishlist.add');
        Route::delete('/{event}/wishlist', [EventController::class, 'removeFromWishlist'])->name('wishlist.remove');
    });
});

// Ticket Routes (Requires Auth)
Route::middleware('auth')->prefix('tickets')->name('tickets.')->group(function () {
    Route::get('/my-tickets', [TicketController::class, 'myTickets'])->name('my-tickets');
    Route::get('/{event}/purchase', [TicketController::class, 'purchase'])->name('purchase');
    Route::post('/{event}', [TicketController::class, 'store'])->name('store');
    Route::get('/{ticket}/show', [TicketController::class, 'show'])->name('show');
    Route::get('/{ticket}/download', [TicketController::class, 'download'])->name('download');
    Route::post('/{ticket}/cancel', [TicketController::class, 'cancel'])->name('cancel');
    Route::post('/validate', [TicketController::class, 'validateTicket'])->name('validate');
});

// Order Routes (Requires Auth)
Route::middleware('auth')->prefix('orders')->name('orders.')->group(function () {
    Route::get('/', [OrderController::class, 'index'])->name('index');
    Route::get('/{order}', [OrderController::class, 'show'])->name('show');
    Route::get('/{order}/payment', [OrderController::class, 'payment'])->name('payment');
    Route::post('/{order}/payment', [OrderController::class, 'processPayment'])->name('process-payment');
    Route::get('/{order}/success', [OrderController::class, 'success'])->name('success');
    Route::post('/{order}/cancel', [OrderController::class, 'cancel'])->name('cancel');
});

// Authentication Routes
Route::middleware('guest')->group(function () {
    // Login
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);

    // Register
    Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);

    // Password Reset
    Route::get('/forgot-password', [PasswordResetController::class, 'showForgotPassword'])->name('password.request');
    Route::post('/forgot-password', [PasswordResetController::class, 'forgotPassword'])->name('password.email');
    Route::get('/reset-password/{token}', [PasswordResetController::class, 'showResetPassword'])->name('password.reset');
    Route::post('/reset-password', [PasswordResetController::class, 'resetPassword'])->name('password.update');
});

// Authenticated Routes
Route::middleware('auth')->group(function () {
    // Logout
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

    // Email Verification
    Route::get('/verify-email', [AuthController::class, 'showVerifyEmail'])->name('auth.verify-email');
    Route::post('/verify-email', [AuthController::class, 'verifyEmail']);
    Route::post('/resend-otp', [AuthController::class, 'resendOtp'])->name('auth.resend-otp');

    // Change Password
    Route::get('/change-password', [PasswordResetController::class, 'showChangePassword'])->name('password.change');
    Route::post('/change-password', [PasswordResetController::class, 'changePassword']);

    // Dashboard Routes (Role-based)
    Route::middleware('admin')->group(function () {
        Route::get('/admin/dashboard', [AdminDashboardController::class, 'index'])->name('admin.dashboard');

        // Admin Event Management
        Route::prefix('admin/events')->name('admin.events.')->group(function () {
            Route::get('/', function () {
                $events = \App\Models\Event::with(['organizer', 'category'])
                    ->withCount('tickets')
                    ->latest()
                    ->paginate(10);
                return view('pages.admin.events', compact('events'));
            })->name('index');

            Route::get('/create', function () {
                $categories = \App\Models\Category::all();
                $organizers = \App\Models\User::where('role', 'penjual')->get();
                return view('pages.admin.events.create', compact('categories', 'organizers'));
            })->name('create');

            Route::post('/', function (\Illuminate\Http\Request $request) {
                // Basic validation
                $request->validate([
                    'title' => 'required|string|max:255',
                    'description' => 'required|string',
                    'category_id' => 'required|exists:categories,id',
                    'organizer_id' => 'required|exists:users,id',
                    'venue_name' => 'required|string|max:255',
                    'venue_address' => 'required|string',
                    'city' => 'required|string|max:100',
                    'start_date' => 'required|date|after:now',
                    'end_date' => 'required|date|after:start_date',
                    'price' => 'required|numeric|min:0',
                    'total_capacity' => 'required|integer|min:1',
                    'poster' => 'required|image|mimes:jpeg,png,jpg|max:2048',
                ]);

                // Handle poster upload
                $posterPath = null;
                if ($request->hasFile('poster')) {
                    $posterPath = $request->file('poster')->store('events/posters', 'public');
                }

                // Create event
                \App\Models\Event::create([
                    'title' => $request->title,
                    'slug' => \Illuminate\Support\Str::slug($request->title),
                    'description' => $request->description,
                    'category_id' => $request->category_id,
                    'organizer_id' => $request->organizer_id,
                    'venue_name' => $request->venue_name,
                    'venue_address' => $request->venue_address,
                    'city' => $request->city,
                    'province' => 'DKI Jakarta', // Default
                    'start_date' => $request->start_date,
                    'end_date' => $request->end_date,
                    'price' => $request->is_free ? 0 : $request->price,
                    'total_capacity' => $request->total_capacity,
                    'available_capacity' => $request->total_capacity,
                    'poster' => $posterPath,
                    'is_free' => $request->boolean('is_free'),
                    'status' => 'published', // Admin created events are auto-published
                ]);

                return redirect()->route('admin.events.index')->with('success', 'Event berhasil dibuat!');
            })->name('store');

            Route::get('/{event}/edit', function (\App\Models\Event $event) {
                $categories = \App\Models\Category::all();
                $organizers = \App\Models\User::where('role', 'penjual')->get();
                return view('pages.admin.events.edit', compact('event', 'categories', 'organizers'));
            })->name('edit');

            Route::put('/{event}', function (\Illuminate\Http\Request $request, \App\Models\Event $event) {
                // Basic validation
                $request->validate([
                    'title' => 'required|string|max:255',
                    'description' => 'required|string',
                    'category_id' => 'required|exists:categories,id',
                    'organizer_id' => 'required|exists:users,id',
                    'venue_name' => 'required|string|max:255',
                    'venue_address' => 'required|string',
                    'city' => 'required|string|max:100',
                    'start_date' => 'required|date',
                    'end_date' => 'required|date|after:start_date',
                    'price' => 'required|numeric|min:0',
                    'total_capacity' => 'required|integer|min:1',
                    'poster' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
                ]);

                $updateData = [
                    'title' => $request->title,
                    'slug' => \Illuminate\Support\Str::slug($request->title),
                    'description' => $request->description,
                    'category_id' => $request->category_id,
                    'organizer_id' => $request->organizer_id,
                    'venue_name' => $request->venue_name,
                    'venue_address' => $request->venue_address,
                    'city' => $request->city,
                    'start_date' => $request->start_date,
                    'end_date' => $request->end_date,
                    'price' => $request->is_free ? 0 : $request->price,
                    'total_capacity' => $request->total_capacity,
                    'is_free' => $request->boolean('is_free'),
                ];

                // Handle poster upload if provided
                if ($request->hasFile('poster')) {
                    $updateData['poster'] = $request->file('poster')->store('events/posters', 'public');
                }

                $event->update($updateData);

                return redirect()->route('admin.events.index')->with('success', 'Event berhasil diperbarui!');
            })->name('update');
        });

        // Admin User Management
        Route::prefix('admin/users')->name('admin.users.')->group(function () {
            Route::get('/', function () {
                $users = \App\Models\User::withCount('organizedEvents')
                    ->latest()
                    ->paginate(10);
                return view('pages.admin.users', compact('users'));
            })->name('index');

            Route::get('/create', function () {
                return view('pages.admin.users.create');
            })->name('create');

            Route::post('/', function (\Illuminate\Http\Request $request) {
                // Basic validation
                $request->validate([
                    'name' => 'required|string|max:255',
                    'email' => 'required|email|unique:users,email',
                    'password' => 'required|string|min:8|confirmed',
                    'role' => 'required|in:admin,staff,penjual,pembeli',
                    'phone' => 'nullable|string|max:20',
                    'date_of_birth' => 'nullable|date',
                    'gender' => 'nullable|in:male,female,other',
                    'address' => 'nullable|string',
                    'profile_photo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
                ]);

                $userData = [
                    'name' => $request->name,
                    'email' => $request->email,
                    'password' => \Illuminate\Support\Facades\Hash::make($request->password),
                    'role' => $request->role,
                    'phone' => $request->phone,
                    'date_of_birth' => $request->date_of_birth,
                    'gender' => $request->gender,
                    'address' => $request->address,
                    'is_active' => $request->boolean('is_active', true),
                ];

                if ($request->boolean('email_verified')) {
                    $userData['email_verified_at'] = now();
                }

                // Handle profile photo upload
                if ($request->hasFile('profile_photo')) {
                    $userData['profile_photo'] = $request->file('profile_photo')->store('users/profiles', 'public');
                }

                \App\Models\User::create($userData);

                return redirect()->route('admin.users.index')->with('success', 'Pengguna berhasil dibuat!');
            })->name('store');

            Route::get('/{user}/edit', function (\App\Models\User $user) {
                return view('pages.admin.users.edit', compact('user'));
            })->name('edit');
        });

        // Admin Payment Management
        Route::prefix('admin/payments')->name('admin.payments.')->group(function () {
            Route::get('/', function () {
                $payments = \App\Models\Order::with(['user', 'event'])
                    ->whereNotNull('payment_method')
                    ->latest()
                    ->paginate(15);

                $stats = [
                    'total_revenue' => \App\Models\Order::where('status', 'completed')->sum('total_amount'),
                    'pending_payments' => \App\Models\Order::where('status', 'pending')->count(),
                    'completed_payments' => \App\Models\Order::where('status', 'completed')->count(),
                    'failed_payments' => \App\Models\Order::where('status', 'failed')->count(),
                ];

                return view('pages.admin.payments', compact('payments', 'stats'));
            })->name('index');

            Route::get('/{order}', function (\App\Models\Order $order) {
                $order->load(['user', 'event', 'tickets']);
                return view('pages.admin.payments.show', compact('order'));
            })->name('show');

            Route::post('/{order}/approve', function (\App\Models\Order $order) {
                $order->update(['status' => 'completed']);
                return redirect()->back()->with('success', 'Payment approved successfully!');
            })->name('approve');

            Route::post('/{order}/reject', function (\App\Models\Order $order) {
                $order->update(['status' => 'failed']);
                return redirect()->back()->with('success', 'Payment rejected successfully!');
            })->name('reject');
        });

        // Admin Notification Management
        Route::prefix('admin/notifications')->name('admin.notifications.')->group(function () {
            Route::get('/', function () {
                $notifications = \App\Models\Notification::with('user')
                    ->latest()
                    ->paginate(15);

                $stats = [
                    'total_notifications' => \App\Models\Notification::count(),
                    'unread_notifications' => \App\Models\Notification::whereNull('read_at')->count(),
                    'system_notifications' => \App\Models\Notification::where('type', 'system')->count(),
                    'user_notifications' => \App\Models\Notification::where('type', 'user')->count(),
                ];

                return view('pages.admin.notifications', compact('notifications', 'stats'));
            })->name('index');

            Route::get('/create', function () {
                $users = \App\Models\User::select('id', 'name', 'email')->get();
                return view('pages.admin.notifications.create', compact('users'));
            })->name('create');

            Route::post('/', function (\Illuminate\Http\Request $request) {
                $request->validate([
                    'title' => 'required|string|max:255',
                    'message' => 'required|string',
                    'type' => 'required|in:system,user,event,payment',
                    'recipients' => 'required|in:all,specific,role',
                    'user_ids' => 'required_if:recipients,specific|array',
                    'role' => 'required_if:recipients,role|in:admin,staff,penjual,pembeli',
                ]);

                $recipients = [];

                if ($request->recipients === 'all') {
                    $recipients = \App\Models\User::pluck('id')->toArray();
                } elseif ($request->recipients === 'specific') {
                    $recipients = $request->user_ids;
                } elseif ($request->recipients === 'role') {
                    $recipients = \App\Models\User::where('role', $request->role)->pluck('id')->toArray();
                }

                foreach ($recipients as $userId) {
                    \App\Models\Notification::create([
                        'user_id' => $userId,
                        'title' => $request->title,
                        'message' => $request->message,
                        'type' => $request->type,
                        'data' => json_encode(['sent_by' => auth()->id()]),
                    ]);
                }

                return redirect()->route('admin.notifications.index')->with('success', 'Notifications sent successfully!');
            })->name('store');
        });

        // Admin Organizer Management
        Route::prefix('admin/organizers')->name('admin.organizers.')->group(function () {
            Route::get('/', function () {
                $organizers = \App\Models\User::where('role', 'penjual')
                    ->withCount(['organizedEvents', 'orders'])
                    ->with(['organizedEvents' => function($query) {
                        $query->select('id', 'organizer_id', 'title', 'status', 'total_capacity')
                              ->withCount('tickets');
                    }])
                    ->latest()
                    ->paginate(10);

                $stats = [
                    'total_organizers' => \App\Models\User::where('role', 'penjual')->count(),
                    'active_organizers' => \App\Models\User::where('role', 'penjual')->where('is_active', true)->count(),
                    'pending_approval' => \App\Models\User::where('role', 'penjual')->whereNull('email_verified_at')->count(),
                    'total_events' => \App\Models\Event::count(),
                ];

                return view('pages.admin.organizers', compact('organizers', 'stats'));
            })->name('index');

            Route::get('/{organizer}', function (\App\Models\User $organizer) {
                $organizer->load(['organizedEvents.category', 'organizedEvents.tickets']);

                $analytics = [
                    'total_events' => $organizer->organizedEvents->count(),
                    'total_revenue' => $organizer->organizedEvents->sum(function($event) {
                        return $event->tickets->where('status', 'used')->sum('price');
                    }),
                    'total_tickets_sold' => $organizer->organizedEvents->sum(function($event) {
                        return $event->tickets->where('status', 'used')->count();
                    }),
                    'avg_event_rating' => 4.5, // Placeholder for rating system
                ];

                return view('pages.admin.organizers.show', compact('organizer', 'analytics'));
            })->name('show');

            Route::post('/{organizer}/approve', function (\App\Models\User $organizer) {
                $organizer->update([
                    'email_verified_at' => now(),
                    'is_active' => true
                ]);

                // Send approval notification
                \App\Models\Notification::create([
                    'user_id' => $organizer->id,
                    'title' => 'Account Approved',
                    'message' => 'Your organizer account has been approved. You can now create and manage events.',
                    'type' => 'system',
                ]);

                return redirect()->back()->with('success', 'Organizer approved successfully!');
            })->name('approve');

            Route::post('/{organizer}/suspend', function (\App\Models\User $organizer) {
                $organizer->update(['is_active' => false]);

                // Send suspension notification
                \App\Models\Notification::create([
                    'user_id' => $organizer->id,
                    'title' => 'Account Suspended',
                    'message' => 'Your organizer account has been suspended. Please contact support for more information.',
                    'type' => 'system',
                ]);

                return redirect()->back()->with('success', 'Organizer suspended successfully!');
            })->name('suspend');

            Route::post('/{organizer}/activate', function (\App\Models\User $organizer) {
                $organizer->update(['is_active' => true]);

                // Send activation notification
                \App\Models\Notification::create([
                    'user_id' => $organizer->id,
                    'title' => 'Account Activated',
                    'message' => 'Your organizer account has been activated. You can now create and manage events.',
                    'type' => 'system',
                ]);

                return redirect()->back()->with('success', 'Organizer activated successfully!');
            })->name('activate');
        });
    });

    Route::middleware('staff')->group(function () {
        Route::get('/staff/dashboard', function () {
            return view('pages.staff.dashboard');
        })->name('staff.dashboard');

        Route::get('/staff/scanner', function () {
            return view('pages.staff.scanner');
        })->name('staff.scanner');
    });

    Route::middleware('organizer')->group(function () {
        Route::get('/organizer/dashboard', [OrganizerDashboardController::class, 'index'])->name('organizer.dashboard');

        // Organizer Event Management
        Route::prefix('organizer/events')->name('organizer.events.')->group(function () {
            Route::get('/', [OrganizerEventController::class, 'index'])->name('index');
            Route::get('/create', [OrganizerEventController::class, 'create'])->name('create');
            Route::post('/', [OrganizerEventController::class, 'store'])->name('store');
            Route::get('/{event}', [OrganizerEventController::class, 'show'])->name('show');
            Route::get('/{event}/edit', [OrganizerEventController::class, 'edit'])->name('edit');
            Route::put('/{event}', [OrganizerEventController::class, 'update'])->name('update');
            Route::delete('/{event}', [OrganizerEventController::class, 'destroy'])->name('destroy');
            Route::post('/{event}/publish', [OrganizerEventController::class, 'publish'])->name('publish');
            Route::post('/{event}/unpublish', [OrganizerEventController::class, 'unpublish'])->name('unpublish');
        });
    });

    // User Dashboard (for pembeli and others)
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');

    // Profile Routes
    Route::get('/profile', [App\Http\Controllers\ProfileController::class, 'show'])->name('profile');
    Route::post('/profile/update', [App\Http\Controllers\ProfileController::class, 'update'])->name('profile.update');
    Route::post('/profile/password', [App\Http\Controllers\ProfileController::class, 'updatePassword'])->name('profile.password');
    Route::post('/profile/photo', [App\Http\Controllers\ProfileController::class, 'updatePhoto'])->name('profile.photo');
    Route::post('/profile/notifications/email', [App\Http\Controllers\ProfileController::class, 'toggleEmailNotification'])->name('profile.notifications.email');
    Route::post('/profile/notifications/push', [App\Http\Controllers\ProfileController::class, 'togglePushNotification'])->name('profile.notifications.push');
    Route::post('/profile/notifications/sms', [App\Http\Controllers\ProfileController::class, 'toggleSmsNotification'])->name('profile.notifications.sms');
    Route::delete('/profile/delete', [App\Http\Controllers\ProfileController::class, 'deleteAccount'])->name('profile.delete');
});
