<?php $__env->startSection('content'); ?>
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- <PERSON><PERSON><PERSON> Header -->
    <div class="mb-8" data-aos="fade-up">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold mb-2">QR Scanner</h1>
                <p class="text-gray-600">Scan QR code tiket untuk validasi</p>
            </div>
            <div class="flex space-x-2">
                <button id="start-scanner" 
                        class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors">
                    <PERSON><PERSON>
                </button>
                <button id="stop-scanner" 
                        class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors" 
                        disabled>
                    Stop Scanner
                </button>
            </div>
        </div>
    </div>

    <!-- Scanner Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Valid Tickets -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="100">
            <div class="flex items-center justify-between">
                <div class="p-3 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Tiket Valid</p>
                    <p class="text-2xl font-bold text-green-600" id="valid-count">0</p>
                </div>
            </div>
        </div>

        <!-- Invalid Tickets -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="200">
            <div class="flex items-center justify-between">
                <div class="p-3 bg-red-100 rounded-lg">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Tiket Invalid</p>
                    <p class="text-2xl font-bold text-red-600" id="invalid-count">0</p>
                </div>
            </div>
        </div>

        <!-- Total Scanned -->
        <div class="bg-white rounded-xl shadow-sm p-6" data-aos="fade-up" data-aos-delay="300">
            <div class="flex items-center justify-between">
                <div class="p-3 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"/>
                    </svg>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Total Scan</p>
                    <p class="text-2xl font-bold text-blue-600" id="total-count">0</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Scanner Interface -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Camera Scanner -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden" data-aos="fade-up" data-aos-delay="400">
            <div class="p-6">
                <h2 class="text-lg font-semibold mb-4">Camera Scanner</h2>
                <div class="relative">
                    <video id="scanner-video" 
                           class="w-full h-64 bg-gray-100 rounded-lg object-cover" 
                           autoplay 
                           muted 
                           playsinline>
                    </video>
                    <div class="absolute inset-0 border-2 border-primary rounded-lg pointer-events-none">
                        <div class="absolute top-4 left-4 w-6 h-6 border-t-2 border-l-2 border-primary"></div>
                        <div class="absolute top-4 right-4 w-6 h-6 border-t-2 border-r-2 border-primary"></div>
                        <div class="absolute bottom-4 left-4 w-6 h-6 border-b-2 border-l-2 border-primary"></div>
                        <div class="absolute bottom-4 right-4 w-6 h-6 border-b-2 border-r-2 border-primary"></div>
                    </div>
                </div>
                <div class="mt-4">
                    <p id="scanner-status" class="text-sm text-gray-600 text-center">
                        Klik "Mulai Scanner" untuk memulai
                    </p>
                </div>
            </div>
        </div>

        <!-- Manual Input -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden" data-aos="fade-up" data-aos-delay="500">
            <div class="p-6">
                <h2 class="text-lg font-semibold mb-4">Input Manual</h2>
                <form id="manual-form" class="space-y-4">
                    <div>
                        <label for="manual-input" class="block text-sm font-medium text-gray-700 mb-2">
                            QR Code atau Nomor Tiket
                        </label>
                        <input type="text" 
                               id="manual-input" 
                               class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                               placeholder="Masukkan QR code atau nomor tiket">
                    </div>
                    <button type="submit" 
                            class="w-full bg-primary text-white py-2 rounded-lg hover:bg-primary/90 transition-colors">
                        Validasi Tiket
                    </button>
                </form>

                <!-- Recent Scans -->
                <div class="mt-6">
                    <h3 class="text-sm font-medium text-gray-700 mb-3">Scan Terbaru</h3>
                    <div id="recent-scans" class="space-y-2 max-h-48 overflow-y-auto">
                        <div class="text-center py-4 text-gray-500 text-sm">
                            Belum ada scan
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scan History -->
    <div class="mt-8 bg-white rounded-xl shadow-sm overflow-hidden" data-aos="fade-up" data-aos-delay="600">
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-lg font-semibold">Riwayat Validasi</h2>
                <button onclick="clearHistory()" 
                        class="text-red-600 hover:text-red-800 transition-colors text-sm">
                    Hapus Riwayat
                </button>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Waktu
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                QR Code
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Event
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Pembeli
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                        </tr>
                    </thead>
                    <tbody id="history-table" class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                                Belum ada riwayat validasi
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div id="success-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl max-w-md w-full p-6">
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                    <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Tiket Valid!</h3>
                <div id="success-details" class="text-sm text-gray-600 space-y-1">
                    <!-- Details will be populated by JavaScript -->
                </div>
                <button onclick="closeModal('success-modal')" 
                        class="mt-4 w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition-colors">
                    OK
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Error Modal -->
<div id="error-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl max-w-md w-full p-6">
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                    <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Tiket Tidak Valid</h3>
                <p id="error-message" class="text-sm text-gray-600 mb-4">
                    <!-- Error message will be populated by JavaScript -->
                </p>
                <button onclick="closeModal('error-modal')" 
                        class="w-full bg-red-600 text-white py-2 rounded-lg hover:bg-red-700 transition-colors">
                    OK
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Scanner functionality will be added here
let scanner = null;
let validCount = 0;
let invalidCount = 0;

function updateStats() {
    document.getElementById('valid-count').textContent = validCount;
    document.getElementById('invalid-count').textContent = invalidCount;
    document.getElementById('total-count').textContent = validCount + invalidCount;
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

function clearHistory() {
    if (confirm('Hapus semua riwayat validasi?')) {
        document.getElementById('history-table').innerHTML = `
            <tr>
                <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                    Belum ada riwayat validasi
                </td>
            </tr>
        `;
        document.getElementById('recent-scans').innerHTML = `
            <div class="text-center py-4 text-gray-500 text-sm">
                Belum ada scan
            </div>
        `;
        validCount = 0;
        invalidCount = 0;
        updateStats();
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Scanner initialization code would go here
    console.log('Scanner page loaded');
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\Project-tikpro.my.id\resources\views/pages/staff/scanner.blade.php ENDPATH**/ ?>